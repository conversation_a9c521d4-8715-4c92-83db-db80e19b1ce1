import time
from typing import Any, Dict, List, Optional

import numpy as np
from pydantic import BaseModel, Field, validator


class StreamCreateRequest(BaseModel):
    url: str
    key: str
    config: Optional[Dict[str, Any]] = Field(default_factory=dict)

    @validator("key")
    def validate_key(cls, v):
        if not v or not v.isalnum():
            raise ValueError("Stream key must be alphanumeric")
        return v

    @validator("url")
    def validate_url(cls, v):
        if not v.startswith(("rtsp://", "http://", "https://", "file://")):
            raise ValueError("URL must be a valid streaming protocol")
        return v


class InferenceRequest:
    def __init__(self, frame: np.ndarray, stream_id: str, timestamp: float = None):
        self.frame = frame
        self.stream_id = stream_id
        self.timestamp = timestamp or time.time()


class DetectionResult(BaseModel):
    xyxy: List[float]
    cls: int
    conf: float = 0.0
