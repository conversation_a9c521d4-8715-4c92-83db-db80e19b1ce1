import asyncio
import subprocess
import time
from typing import Dict, List

import cv2
import numpy as np
import ray
import tritonclient.http.aio as aiohttpclient
import uvicorn
from fastapi import FastAPI, HTTPException
from ray.util.queue import Queue as RayQueue

from config import MonitoringConfig, StreamConfig, StreamMetrics
from models import DetectionResult, InferenceRequest, StreamCreateRequest

# 初始化配置
config = StreamConfig()
monitoring_config = MonitoringConfig()

# 初始化Ray
ray.init(address="auto")

app = FastAPI(title="Ray Video Stream Processor")


@ray.remote
class InferenceService:
    """分布式推理服务，支持多实例部署"""

    def __init__(self, triton_url: str, model_name: str, batch_size: int = 4):
        self.triton_url = triton_url
        self.model_name = model_name
        self.batch_size = batch_size
        self.client = None
        self.running = True
        self.health_status = "initializing"

    async def initialize(self):
        try:
            self.client = aiohttpclient.InferenceServerClient(url=self.triton_url)
            is_ready = await self.client.is_server_ready()
            if is_ready:
                self.health_status = "healthy"
            else:
                self.health_status = "unhealthy"
        except Exception as e:
            self.health_status = f"error: {str(e)}"
            raise RuntimeError(f"Failed to initialize Triton client: {str(e)}")

    async def process_batch(
        self, requests: List[InferenceRequest]
    ) -> Dict[str, List[DetectionResult]]:
        if not requests:
            return {}

        start_time = time.time()

        # 预处理
        imgs = []
        for req in requests:
            img = cv2.resize(req.frame, (640, 640))
            img = img.astype(np.float32) / 255.0
            img = np.transpose(img, (2, 0, 1))  # CHW
            imgs.append(img)
        imgs = np.stack(imgs, axis=0)

        # 推理
        inputs = [aiohttpclient.InferInput("images", imgs.shape, "FP32")]
        inputs[0].set_data_from_numpy(imgs, binary_data=True)
        outputs = [aiohttpclient.InferRequestedOutput("output0")]

        try:
            response = await self.client.infer(
                model_name=self.model_name, inputs=inputs, outputs=outputs
            )
            result = response.as_numpy("output0")

            # 后处理
            results_dict = {}
            for i, req in enumerate(requests):
                boxes = self._parse_triton_output(result[i])
                results_dict[req.stream_id] = boxes

            return results_dict

        except Exception as e:
            print(f"Inference error: {str(e)}")
            return {req.stream_id: [] for req in requests}

    def _parse_triton_output(self, output):
        boxes = []
        for det in output:
            if det[4] > 0.4:
                x1, y1, x2, y2, conf, cls = det
                boxes.append(
                    DetectionResult(
                        xyxy=[float(x1), float(y1), float(x2), float(y2)],
                        cls=int(cls),
                        conf=float(conf),
                    )
                )
        return boxes

    async def get_health(self):
        return {
            "status": self.health_status,
            "triton_url": self.triton_url,
            "model": self.model_name,
        }


@ray.remote
class StreamManager:
    """流管理器，负责协调推理资源"""

    def __init__(self):
        self.inference_pool = []
        self.request_queue = RayQueue(maxsize=1000)
        self.running = True
        self.worker_task = None

    async def initialize(self):
        self.worker_task = asyncio.create_task(self.distribute_requests())

    async def add_inference_worker(self, worker_ref):
        self.inference_pool.append(worker_ref)

    async def infer(self, request: InferenceRequest):
        """单个推理请求接口"""
        future = asyncio.Future()
        await self.request_queue.put((request, future))

        try:
            result = await asyncio.wait_for(future, timeout=config.inference_timeout)
            return result
        except asyncio.TimeoutError:
            future.cancel()
            raise asyncio.TimeoutError("Inference request timed out")

    async def distribute_requests(self):
        """负载均衡分发推理请求"""
        batch = []
        futures = []
        last_process_time = time.time()

        while self.running:
            try:
                # 收集请求到批次
                while len(batch) < config.batch_size:
                    try:
                        req, future = await asyncio.wait_for(
                            self.request_queue.get_async(), timeout=0.01
                        )
                        batch.append(req)
                        futures.append(future)
                    except asyncio.TimeoutError:
                        break

                # 如果批次为空或者太小且等待时间不长，继续等待
                if not batch:
                    await asyncio.sleep(0.005)
                    continue

                current_time = time.time()
                if (
                    len(batch) < config.batch_size
                    and current_time - last_process_time < 0.03
                ):
                    # 等待更多请求以填充批次，除非已经等待太久
                    if self.request_queue.empty():
                        await asyncio.sleep(0.005)
                    continue

                # 选择推理worker (简单轮询)
                if not self.inference_pool:
                    for future in futures:
                        future.set_result([])
                    batch.clear()
                    futures.clear()
                    continue

                worker_idx = int(current_time * 10) % len(self.inference_pool)
                worker = self.inference_pool[worker_idx]

                # 发送批量请求
                try:
                    results = await worker.process_batch.remote(batch)

                    # 分发结果
                    for i, future in enumerate(futures):
                        stream_id = batch[i].stream_id
                        if stream_id in results:
                            future.set_result(results[stream_id])
                        else:
                            future.set_result([])

                except Exception as e:
                    for future in futures:
                        future.set_exception(e)

                # 重置批次
                batch = []
                futures = []
                last_process_time = time.time()

            except Exception as e:
                print(f"Error in distribute_requests: {str(e)}")
                await asyncio.sleep(0.1)

    async def shutdown(self):
        self.running = False
        if self.worker_task:
            self.worker_task.cancel()

    async def get_stats(self):
        return {
            "queue_size": self.request_queue.size(),
            "workers": len(self.inference_pool),
        }


@ray.remote
class VideoStreamProcessor:
    """重构后的视频流处理器"""

    def __init__(self, stream_config: dict, stream_manager_ref):
        self.config = stream_config
        self.stream_manager = stream_manager_ref
        self.cap = None
        self.ffmpeg_proc = None
        self.running = True
        self.metrics = StreamMetrics()
        self.processing_task = None

    async def initialize(self):
        """初始化资源"""
        try:
            self.cap = cv2.VideoCapture(self.config["url"])
            if not self.cap.isOpened():
                raise RuntimeError(f"Failed to open stream: {self.config['url']}")

            # 初始化FFmpeg进程
            self.ffmpeg_proc = await self._create_ffmpeg_process()

            # 启动处理任务
            self.processing_task = asyncio.create_task(self.process_stream())
            return True
        except Exception as e:
            print(f"Initialization error: {str(e)}")
            await self.cleanup()
            raise

    async def _create_ffmpeg_process(self) -> subprocess.Popen:
        """创建FFmpeg进程，支持重连"""
        width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = self.cap.get(cv2.CAP_PROP_FPS) or 25

        cmd = [
            "ffmpeg",
            "-y",
            "-f",
            "rawvideo",
            "-vcodec",
            "rawvideo",
            "-pix_fmt",
            "bgr24",
            "-s",
            f"{width}x{height}",
            "-r",
            str(fps),
            "-i",
            "-",
            "-c:v",
            "libx264",
            "-preset",
            "ultrafast",
            "-tune",
            "zerolatency",
            "-pix_fmt",
            "yuv420p",
            "-f",
            "flv",
            f"{config.rtmp_base_url}/{self.config['key']}",
        ]

        return subprocess.Popen(cmd, stdin=subprocess.PIPE, stderr=subprocess.PIPE)

    async def process_stream(self):
        """主处理循环，增强错误处理"""
        retry_count = 0
        max_retries = 3

        while self.running and retry_count < max_retries:
            try:
                await self._process_frame_loop()
                retry_count = 0  # 重置重试计数
            except Exception as e:
                retry_count += 1
                print(
                    f"Stream processing error ({retry_count}/{max_retries}): {str(e)}"
                )

                if retry_count < max_retries:
                    # 尝试重新初始化资源
                    await self.cleanup()
                    try:
                        self.cap = cv2.VideoCapture(self.config["url"])
                        self.ffmpeg_proc = await self._create_ffmpeg_process()
                        await asyncio.sleep(1)  # 等待重连
                    except Exception as init_error:
                        print(f"Failed to reinitialize: {str(init_error)}")

        # 处理循环结束，清理资源
        await self.cleanup()

    async def _process_frame_loop(self):
        """帧处理循环"""
        consecutive_errors = 0

        while self.running:
            if not self.cap.isOpened():
                raise RuntimeError("Video capture is closed")

            ret, frame = self.cap.read()
            if not ret:
                consecutive_errors += 1
                if consecutive_errors > 10:
                    raise RuntimeError("Too many consecutive frame read failures")
                await asyncio.sleep(0.1)
                continue

            consecutive_errors = 0

            # 发送推理请求
            request = InferenceRequest(
                frame=frame, stream_id=self.config["key"], timestamp=time.time()
            )

            try:
                start_time = time.time()
                result = await self.stream_manager.infer.remote(request)
                latency = time.time() - start_time

                processed_frame = self._draw_detections(frame, result)
                await self._write_frame(processed_frame)

                self.metrics.update_success(latency)
            except asyncio.TimeoutError:
                self.metrics.update_timeout()
                await self._write_frame(frame)  # 发送原始帧
            except Exception as e:
                self.metrics.update_error()
                print(f"Frame processing error: {str(e)}")
                try:
                    await self._write_frame(frame)  # 尝试发送原始帧
                except:
                    pass  # 忽略写入错误

    def _draw_detections(self, frame, detections):
        """在帧上绘制检测结果"""
        for det in detections:
            x1, y1, x2, y2 = map(int, det.xyxy)
            cls = det.cls
            conf = det.conf

            # 绘制边界框
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # 绘制标签
            label = f"{cls}: {conf:.2f}"
            cv2.putText(
                frame,
                label,
                (x1, y1 - 10),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.6,
                (0, 255, 0),
                2,
            )

        return frame

    async def _write_frame(self, frame):
        """将帧写入FFmpeg进程"""
        if self.ffmpeg_proc and self.ffmpeg_proc.poll() is None:
            try:
                self.ffmpeg_proc.stdin.write(frame.tobytes())
            except BrokenPipeError:
                # FFmpeg进程已关闭，尝试重新创建
                self.ffmpeg_proc = await self._create_ffmpeg_process()
                self.ffmpeg_proc.stdin.write(frame.tobytes())
        else:
            # 进程不存在或已结束，重新创建
            self.ffmpeg_proc = await self._create_ffmpeg_process()
            self.ffmpeg_proc.stdin.write(frame.tobytes())

    async def stop(self):
        """停止处理"""
        self.running = False
        if self.processing_task:
            self.processing_task.cancel()
        await self.cleanup()

    async def cleanup(self):
        """清理资源"""
        if self.cap:
            self.cap.release()
            self.cap = None

        if self.ffmpeg_proc:
            try:
                self.ffmpeg_proc.stdin.close()
                self.ffmpeg_proc.terminate()
                await asyncio.sleep(0.5)
                if self.ffmpeg_proc.poll() is None:
                    self.ffmpeg_proc.kill()
            except:
                pass
            self.ffmpeg_proc = None

    async def get_metrics(self):
        """获取处理指标"""
        return {
            "frames_processed": self.metrics.frames_processed,
            "inference_latency": self.metrics.inference_latency,
            "error_count": self.metrics.error_count,
            "timeout_count": self.metrics.timeout_count,
            "last_update": self.metrics.last_update,
            "stream_key": self.config["key"],
            "url": self.config["url"],
        }


class StreamAPI:
    def __init__(self):
        self.stream_manager = None
        self.active_streams = {}
        self.inference_workers = []

    async def initialize(self):
        """初始化流管理器和推理服务池"""
        self.stream_manager = StreamManager.remote()
        await self.stream_manager.initialize.remote()

        # 启动推理服务池
        for i in range(3):  # 3个推理worker
            worker = InferenceService.remote(
                triton_url=config.triton_url,
                model_name=config.model_name,
                batch_size=config.batch_size,
            )
            await worker.initialize.remote()
            await self.stream_manager.add_inference_worker.remote(worker)
            self.inference_workers.append(worker)

    async def create_stream(self, request: StreamCreateRequest):
        """创建新流，增强验证和错误处理"""
        if request.key in self.active_streams:
            raise HTTPException(409, f"Stream {request.key} already exists")

        try:
            # 创建流处理器
            processor = VideoStreamProcessor.remote(
                stream_config=request.dict(), stream_manager_ref=self.stream_manager
            )

            # 初始化并启动处理
            success = await processor.initialize.remote()
            if not success:
                raise HTTPException(500, "Failed to initialize stream processor")

            self.active_streams[request.key] = processor
            return {"status": "created", "key": request.key}

        except Exception as e:
            raise HTTPException(500, f"Failed to create stream: {str(e)}")

    async def stop_stream(self, stream_key: str):
        """停止流处理"""
        if stream_key not in self.active_streams:
            raise HTTPException(404, "Stream not found")

        processor = self.active_streams[stream_key]
        try:
            await processor.stop.remote()
            del self.active_streams[stream_key]
            return {"status": "stopped", "key": stream_key}
        except Exception as e:
            raise HTTPException(500, f"Failed to stop stream: {str(e)}")

    async def get_stream_metrics(self, stream_key: str):
        """获取流处理指标"""
        if stream_key not in self.active_streams:
            raise HTTPException(404, "Stream not found")

        processor = self.active_streams[stream_key]
        metrics = await processor.get_metrics.remote()
        return metrics

    async def get_all_streams(self):
        """获取所有活跃流"""
        streams = []
        for key, processor in self.active_streams.items():
            try:
                metrics = await processor.get_metrics.remote()
                streams.append(metrics)
            except:
                streams.append({"stream_key": key, "status": "error"})
        return streams

    async def get_system_stats(self):
        """获取系统状态"""
        manager_stats = await self.stream_manager.get_stats.remote()

        worker_stats = []
        for worker in self.inference_workers:
            try:
                health = await worker.get_health.remote()
                worker_stats.append(health)
            except:
                worker_stats.append({"status": "error"})

        return {
            "active_streams": len(self.active_streams),
            "manager": manager_stats,
            "workers": worker_stats,
        }


# 创建API实例
stream_api = StreamAPI()


@app.on_event("startup")
async def startup():
    await stream_api.initialize()


@app.post("/streams")
async def create_stream(request: StreamCreateRequest):
    return await stream_api.create_stream(request)


@app.delete("/streams/{stream_key}")
async def stop_stream(stream_key: str):
    return await stream_api.stop_stream(stream_key)


@app.get("/streams/{stream_key}/metrics")
async def get_stream_metrics(stream_key: str):
    return await stream_api.get_stream_metrics(stream_key)


@app.get("/streams")
async def get_all_streams():
    return await stream_api.get_all_streams()


@app.get("/system/stats")
async def get_system_stats():
    return await stream_api.get_system_stats()


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
