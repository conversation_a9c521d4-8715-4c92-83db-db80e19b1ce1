from pydantic import BaseSettings, Field
from typing import Dict, Any
from dataclasses import dataclass
import time

class StreamConfig(BaseSettings):
    triton_url: str = "localhost:8001"
    model_name: str = "yolov8"
    batch_size: int = 4
    max_concurrent_streams: int = 10
    inference_timeout: float = 2.0
    rtmp_base_url: str = "rtmp://localhost/live"
    
    # 资源限制
    max_memory_per_stream: str = "1GB"
    max_cpu_per_stream: float = 1.0
    
class MonitoringConfig(BaseSettings):
    enable_metrics: bool = True
    metrics_port: int = 9090
    log_level: str = "INFO"

@dataclass
class StreamMetrics:
    frames_processed: int = 0
    inference_latency: float = 0.0
    error_count: int = 0
    timeout_count: int = 0
    last_update: float = time.time()
    
    def update_success(self, latency: float = 0.0):
        self.frames_processed += 1
        self.inference_latency = latency
        self.last_update = time.time()
        
    def update_error(self):
        self.error_count += 1
        self.last_update = time.time()
        
    def update_timeout(self):
        self.timeout_count += 1
        self.last_update = time.time()