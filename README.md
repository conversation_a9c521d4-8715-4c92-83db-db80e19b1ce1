# Ray Video Stream Processor

分布式视频流处理系统，基于Ray和Triton Inference Server。

## 主要特性

- 分布式推理服务池，支持水平扩展
- 批量推理优化，提高GPU利用率
- 容错设计，支持自动重连和故障恢复
- 完整的监控和指标收集
- RESTful API接口

## 架构设计

系统由以下核心组件组成：

1. **StreamManager**: 负责管理推理请求队列和负载均衡
2. **InferenceService**: 分布式推理服务，可部署多实例
3. **VideoStreamProcessor**: 视频流处理器，每个流一个实例
4. **API层**: RESTful接口，用于管理流和监控系统

## 快速开始

### 前提条件

- Ray集群
- Triton Inference Server (已部署YOLOv8模型)
- RTMP服务器 (如Nginx-RTMP)

### 安装依赖

```bash
pip install ray[default] opencv-python tritonclient fastapi uvicorn pydantic
```

### 启动服务

```bash
python ray_streamer_v2.py
```

### API使用示例

创建新流:
```bash
curl -X POST http://localhost:8000/streams \
  -H "Content-Type: application/json" \
  -d '{"url": "rtsp://example.com/stream1", "key": "stream1"}'
```

停止流:
```bash
curl -X DELETE http://localhost:8000/streams/stream1
```

获取流指标:
```bash
curl http://localhost:8000/streams/stream1/metrics
```

## 配置

编辑`config.py`文件调整系统配置。

